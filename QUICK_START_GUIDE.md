# 🚀 Emynent Quick Start Guide

**Get running in under 10 minutes!** This is the TL;DR version for experienced developers.

## ⚡ Super Quick Setup (Automated)

```bash
# Clone and run automated setup
git clone <repository-url>
cd emynent
chmod +x scripts/setup-dev-environment.sh
./scripts/setup-dev-environment.sh
```

The script will handle everything automatically. Skip to [Verification](#verification) when done.

## 🛠️ Manual Setup (5-10 minutes)

### 1. Prerequisites Check
```bash
node --version    # Need 20+
psql --version    # Need 15+
redis-server --version  # Need 7+
```

### 2. Quick Install
```bash
# Clone and install
git clone <repository-url>
cd emynent
npm install

# Environment setup
cp config/environments/.env.example .env.local
```

### 3. Configure Environment
Edit `.env.local` with your values:
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/emynent_dev"

# Auth
NEXTAUTH_SECRET="your-secret-here"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth (get from Google Cloud Console)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Redis
REDIS_URL="redis://localhost:6379"
```

### 4. Database Setup
```bash
# Create database
createdb emynent_dev

# Run migrations and seed
npx prisma migrate dev
npx prisma db seed
```

### 5. Start Services
```bash
# Terminal 1: Redis
redis-server

# Terminal 2: Development server
npm run dev
```

## 🔧 Quick Fixes for Common Issues

### Port 3000 in use:
```bash
npx kill-port 3000
```

### Database connection issues:
```bash
# Reset database
dropdb emynent_dev && createdb emynent_dev
npx prisma migrate dev
```

### Redis not running:
```bash
# macOS
brew services start redis

# Linux
sudo systemctl start redis
```

### TypeScript errors:
```bash
npm run type-check
npm run lint:fix
```

## ✅ Verification

1. **Health Check**: Visit http://localhost:3000/api/health
2. **App loads**: Visit http://localhost:3000
3. **Auth works**: Try Google sign-in
4. **Tests pass**: Run `npm test`

## 🆘 Need Help?

- **Detailed setup**: See [README.md](./README.md)
- **Step-by-step**: See [SETUP_CHECKLIST.md](./SETUP_CHECKLIST.md)
- **Issues**: Check the troubleshooting section in README.md

## 🎯 Success Criteria

You're ready when:
- ✅ Dev server starts without errors
- ✅ Health endpoint returns green
- ✅ Google OAuth sign-in works
- ✅ User dashboard loads after auth
- ✅ Basic tests pass

**Total time: 5-10 minutes for experienced developers**
