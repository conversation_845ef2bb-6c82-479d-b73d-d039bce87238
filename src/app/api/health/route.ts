import { NextRequest, NextResponse } from 'next/server'
import { checkDatabaseHealth, quickHealthCheck } from '@/lib/database-health'
import { createRateLimit } from '@/lib/rate-limit'

// Rate limiter for health checks (higher limit since this is used for monitoring)
const healthCheckRateLimiter = createRateLimit({
  limit: 60,
  windowMs: 60 * 1000, // 1 minute
  message: 'Too many health check requests, please try again later',
})

/**
 * GET /api/health
 *
 * Health check endpoint for monitoring database and service status
 * Supports both quick and detailed health checks
 */
export async function GET(req: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await healthCheckRateLimiter(req)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Check if this is a quick health check
    const { searchParams } = new URL(req.url)
    const quick = searchParams.get('quick') === 'true'

    if (quick) {
      // Quick health check for load balancers and basic monitoring
      const result = await quickHealthCheck()

      return NextResponse.json(result, {
        status: result.status === 'healthy' ? 200 : 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })
    }

    // Comprehensive health check
    const healthStatus = await checkDatabaseHealth()

    // Determine HTTP status code based on health
    let statusCode: number
    switch (healthStatus.status) {
      case 'healthy':
        statusCode = 200
        break
      case 'degraded':
        statusCode = 200 // Still operational but with warnings
        break
      case 'unhealthy':
        statusCode = 503 // Service unavailable
        break
      default:
        statusCode = 500
    }

    return NextResponse.json(healthStatus, {
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    })
  } catch (error) {
    console.error('Health check failed:', error)

    return NextResponse.json(
      {
        status: 'unhealthy',
        error: 'Health check failed',
        timestamp: new Date(),
        responseTime: 0,
      },
      {
        status: 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      }
    )
  }
}
