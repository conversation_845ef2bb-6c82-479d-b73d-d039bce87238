// Debug script to check landing page rendering issues
// This script will help identify why Navbar and Hero components aren't rendering

console.log('🔍 Landing Page Debug Script Starting...');

// Check if we're in the browser
if (typeof window !== 'undefined') {
  console.log('✅ Running in browser environment');
  
  // 1. Check if React components are mounting
  setTimeout(() => {
    console.log('\n📋 DOM Analysis:');
    
    // Check for main landing page container
    const mainContainer = document.querySelector('main');
    console.log('Main container found:', !!mainContainer);
    if (mainContainer) {
      console.log('Main container classes:', mainContainer.className);
      console.log('Main container children count:', mainContainer.children.length);
    }
    
    // Check for sparkles (working component)
    const sparklesCanvas = document.querySelector('canvas');
    console.log('Sparkles canvas found:', !!sparklesCanvas);
    
    // Check for navbar
    const navbar = document.querySelector('nav');
    console.log('Navbar found:', !!navbar);
    
    // Check for hero section
    const heroSection = document.querySelector('[class*="hero"]') || 
                       document.querySelector('h1') ||
                       document.querySelector('[class*="text-6xl"]');
    console.log('Hero section found:', !!heroSection);
    
    // 2. Check CSS variables
    console.log('\n🎨 CSS Variables Check:');
    const rootStyles = getComputedStyle(document.documentElement);
    const cssVars = [
      '--primary',
      '--primary-foreground', 
      '--background',
      '--foreground',
      '--accent',
      '--brand-purple-primary'
    ];
    
    cssVars.forEach(varName => {
      const value = rootStyles.getPropertyValue(varName);
      console.log(`${varName}:`, value || 'NOT FOUND');
    });
    
    // 3. Check for JavaScript errors in console
    console.log('\n⚠️  Check browser console for any error messages above this line');
    
    // 4. Check React component mounting
    console.log('\n⚛️  React Component Check:');
    const reactRoot = document.querySelector('#__next') || document.querySelector('[data-reactroot]');
    console.log('React root found:', !!reactRoot);
    
    // 5. Check for theme provider context
    console.log('\n🎭 Theme Context Check:');
    try {
      // This will help us see if theme context is working
      const bodyClasses = document.body.className;
      console.log('Body classes:', bodyClasses);
      console.log('Dark mode active:', bodyClasses.includes('dark'));
    } catch (e) {
      console.error('Theme context error:', e);
    }
    
  }, 2000); // Wait 2 seconds for components to mount
  
} else {
  console.log('❌ Not in browser environment');
}
